import torch
from pathlib import Path
from data.eng_ita import EngItaTranslationDataset

# 工作目录，缓存文件和模型会放在该目录下
base_dir = r"D:\2025-up\MT\TransformerTurnDistillation\train_process\transformer-eng-ita"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/transformer_checkpoints")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/ita-eng/ita.txt"

dataset = EngItaTranslationDataset(data_dir)
max_seq_length = 42

def translate(src: str, debug=False):
    model = torch.load(model_dir / 'best.pt', map_location=device)
    model.to(device)
    model.eval()

    # 分词并转换为索引
    en_tokens = dataset.en_tokenizer(src)
    if debug:
        print(f"英语分词结果: {en_tokens}")
    
    en_indices = dataset.en_vocab(en_tokens)
    if debug:
        print(f"英语索引: {en_indices}")
    
    src_tensor = torch.tensor([0] + en_indices + [1], dtype=torch.long).unsqueeze(0).to(device)
    if debug:
        print(f"源语言张量: {src_tensor}")

    # 从 <bos> 开始
    tgt = torch.tensor([[0]], dtype=torch.long, device=device)

    with torch.no_grad():
        for i in range(max_seq_length):
            out = model(src_tensor, tgt)
            predict = model.predictor(out[:, -1])
            y = torch.argmax(predict, dim=1)
            tgt = torch.cat([tgt, y.unsqueeze(0)], dim=1)
            
            if debug:
                print(f"步骤 {i}: 预测token = {y.item()}, 对应词汇 = {dataset.ita_vocab.lookup_token(y.item())}")
            
            if y.item() == 1:  # <eos>
                break

    tgt_tokens = tgt.squeeze().tolist()
    if debug:
        print(f"目标语言tokens: {tgt_tokens}")
    
    tgt_sentence = " ".join(dataset.ita_vocab.lookup_tokens(tgt_tokens))
    tgt_sentence = tgt_sentence.replace("<s>", "").replace("</s>", "").strip()
    return tgt_sentence


if __name__ == "__main__":
    print("=== 英意翻译测试 ===")
    
    # 先用调试模式测试一个简单的句子
    print("\n--- 调试模式测试 ---")
    result = translate("Hi", debug=True)
    print(f"翻译结果: {result}")
    
    print("\n--- 正常翻译测试 ---")
    test_sentences = [
        "Hi",
        "Run", 
        "Who",
        "Wow",
        "Duck"
    ]
    
    for sentence in test_sentences:
        result = translate(sentence)
        print(f"英语: {sentence} -> 意大利语: {result}")
    
    print("\n--- 复杂句子测试 ---")
    complex_sentences = [
        "Hello world",
        "How are you",
        "I love you",
        "Good morning",
        "Thank you very much"
    ]
    
    for sentence in complex_sentences:
        result = translate(sentence)
        print(f"英语: {sentence}")
        print(f"意大利语: {result}")
        print("---")
