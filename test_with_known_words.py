from data.ru_zh import RuZhTranslationDataset
from inference_ru_transformer import translate

dataset = RuZhTranslationDataset('data/ru-zh.txt')

print("=== 使用训练数据中的词汇测试翻译 ===")

# 从词汇表中选择一些有意义的词汇进行测试
known_words = []
for i in range(4, min(50, len(dataset.ru_vocab))):
    word = dataset.ru_vocab.lookup_token(i)
    if len(word) > 2 and word.isalpha():  # 选择长度大于2的纯字母词汇
        known_words.append(word)

print(f"找到 {len(known_words)} 个已知词汇")

# 测试单个词汇
print("\n--- 单词翻译测试 ---")
for word in known_words[:10]:  # 测试前10个
    result = translate(word)
    print(f"俄语: {word} -> 中文: {result}")

# 测试简单的词汇组合
print("\n--- 词汇组合测试 ---")
simple_combinations = [
    "в сша",
    "как это", 
    "для войны",
    "более лет",
    "по смысле"
]

for combo in simple_combinations:
    tokens = dataset.ru_tokenizer(combo)
    indices = dataset.ru_vocab(tokens)
    unk_count = indices.count(3)
    coverage = (len(indices) - unk_count) / len(indices) * 100
    
    result = translate(combo)
    print(f"俄语: {combo} (覆盖率: {coverage:.1f}%) -> 中文: {result}")

# 查找训练数据中的完整句子示例
print("\n--- 训练数据示例 ---")
print("让我们看看训练数据中的一些实际句子...")

# 读取几行训练数据作为示例
with open('data/ru-zh.txt', 'r', encoding='utf-8') as f:
    lines = f.readlines()

print("训练数据中的句子示例:")
for i, line in enumerate(lines[200:210]):  # 显示第200-210行
    parts = line.strip().split('\t')
    if len(parts) >= 4:
        ru_text = parts[2].strip()
        zh_text = parts[3].strip()
        if len(ru_text) > 5 and len(zh_text) > 2:
            print(f"{i+200}: {ru_text} -> {zh_text}")
