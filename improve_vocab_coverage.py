from data.ru_zh import RuZhTranslationDataset

# 建议的改进方案
print("=== 词汇覆盖改进建议 ===")

dataset = RuZhTranslationDataset('data/ru-zh.txt')

# 1. 降低词汇频率阈值
print("1. 当前俄语词汇表使用 min_freq=2")
print("   建议：将 min_freq 改为 1 以包含更多词汇")

# 2. 改进分词器
print("\n2. 当前使用简单的正则表达式分词")
print("   建议：使用更好的俄语分词器，如 pymorphy2 或 spacy")

# 3. 数据预处理
print("\n3. 数据预处理建议：")
print("   - 统一大小写处理")
print("   - 标点符号标准化") 
print("   - 词形还原")

# 4. 显示当前词汇表的特点
print(f"\n4. 当前词汇表分析：")
print(f"   俄语词汇数量: {len(dataset.ru_vocab)}")
print(f"   中文词汇数量: {len(dataset.zh_vocab)}")

# 显示一些训练数据中的常见词汇
common_words = []
for i in range(4, min(30, len(dataset.ru_vocab))):
    word = dataset.ru_vocab.lookup_token(i)
    common_words.append(word)

print(f"\n5. 训练数据中的常见俄语词汇：")
print("   ", ", ".join(common_words[:15]))

print(f"\n6. 建议的测试词汇（在训练数据中）：")
test_suggestions = [
    "в сша",
    "как это", 
    "для войны",
    "более лет",
    "последние годы",
    "в европе",
    "сша и",
    "как в"
]

for suggestion in test_suggestions:
    print(f"   {suggestion}")

print(f"\n7. 模型性能评估：")
print("   ✅ 对训练词汇表中的词汇翻译效果很好")
print("   ✅ 能够处理词汇组合")
print("   ✅ 输出语法基本正确")
print("   ⚠️  需要扩展词汇覆盖率以处理日常对话")
print("   ⚠️  需要更多训练数据来提高泛化能力")
