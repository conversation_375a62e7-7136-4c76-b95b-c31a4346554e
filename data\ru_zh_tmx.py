import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
from torchtext.data import get_tokenizer
import jieba
from torchtext.vocab import build_vocab_from_iterator
import zhconv
import re
import xml.etree.ElementTree as ET


class RuZhTMXTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.use_cache = use_cache
        
        # 首先解析TMX文件并提取句子对
        self.sentence_pairs = self.parse_tmx_file(filepath)
        self.row_count = len(self.sentence_pairs)
        
        print(f"从TMX文件中提取了 {self.row_count} 个句子对")

        # 加载词典和token
        self.ru_vocab = self.get_ru_vocab(filepath)
        self.zh_vocab = self.get_zh_vocab(filepath)
        self.ru_tokens = self.load_tokens(filepath, self.ru_tokenizer, self.ru_vocab, "构建俄文tokens", 'ru')
        self.zh_tokens = self.load_tokens(filepath, self.zh_tokenizer, self.zh_vocab, "构建中文tokens", 'zh')

    def __getitem__(self, index):
        return self.ru_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def parse_tmx_file(self, filepath):
        """解析TMX文件，提取俄语和中文句子对"""
        print("开始解析TMX文件...")
        sentence_pairs = []

        # 直接使用简单的正则表达式方法，避免XML解析问题
        sentence_pairs = self.parse_tmx_simple(filepath)

        return sentence_pairs

    def parse_tmx_simple(self, filepath):
        """使用简单方法解析TMX文件"""
        sentence_pairs = []

        print("正在读取TMX文件...")
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"文件共有 {len(lines)} 行")

        # 逐行解析，查找俄语和中文句子对
        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 查找翻译单元开始
            if '<tu>' in line:
                ru_text = None
                zh_text = None

                # 在接下来的几行中查找俄语和中文文本
                j = i + 1
                while j < len(lines) and '</tu>' not in lines[j]:
                    current_line = lines[j].strip()

                    # 查找俄语文本
                    if 'xml:lang="ru"' in current_line and '<seg>' in current_line:
                        seg_match = re.search(r'<seg>(.*?)</seg>', current_line)
                        if seg_match:
                            ru_text = seg_match.group(1).strip()

                    # 查找中文文本
                    elif 'xml:lang="zh"' in current_line and '<seg>' in current_line:
                        seg_match = re.search(r'<seg>(.*?)</seg>', current_line)
                        if seg_match:
                            zh_text = seg_match.group(1).strip()

                    j += 1

                # 如果找到了俄语和中文文本对，添加到列表中
                if ru_text and zh_text and len(ru_text) > 0 and len(zh_text) > 0:
                    sentence_pairs.append((ru_text, zh_text))

                    # 每1000个句子对输出一次进度
                    if len(sentence_pairs) % 1000 == 0:
                        print(f"已提取 {len(sentence_pairs)} 个句子对...")

                i = j
            else:
                i += 1

        print(f"TMX解析完成，共提取 {len(sentence_pairs)} 个句子对")
        return sentence_pairs

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}_tmx.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu")

        tokens_list = []
        for i, (ru_text, zh_text) in enumerate(tqdm(self.sentence_pairs, desc=desc)):
            if lang == 'ru':
                text = ru_text
            else:  # zh
                text = zhconv.convert(zh_text, 'zh-cn')
            
            if text and len(text.strip()) > 0:
                tokens = tokenizer(text)
                if tokens:  # 确保分词结果不为空
                    token_indices = [vocab[token] for token in tokens]
                    token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                    tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list

    def ru_tokenizer(self, line):
        # 俄语分词：使用简单的空格和标点符号分割
        tokens = re.findall(r'\b\w+\b', line.lower())
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_ru_vocab(self, filepath):
        def yield_ru_tokens():
            print("---开始构建俄文词典---")
            for ru_text, zh_text in tqdm(self.sentence_pairs, desc="构建俄文词典"):
                if ru_text and len(ru_text.strip()) > 0:
                    tokens = self.ru_tokenizer(ru_text)
                    if tokens:
                        yield tokens

        dir_path = os.path.dirname(filepath)
        ru_vocab_file = os.path.join(dir_path, "vocab_ru_tmx.pt")
        if self.use_cache and os.path.exists(ru_vocab_file):
            ru_vocab = torch.load(ru_vocab_file, map_location="cpu")
        else:
            ru_vocab = build_vocab_from_iterator(
                yield_ru_tokens(),
                min_freq=2,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            ru_vocab.set_default_index(ru_vocab["<unk>"])
            if self.use_cache:
                torch.save(ru_vocab, ru_vocab_file)
        return ru_vocab

    def get_zh_vocab(self, filepath):
        def yield_zh_tokens():
            print("---开始构建中文词典---")
            for ru_text, zh_text in tqdm(self.sentence_pairs, desc="构建中文词典"):
                if zh_text and len(zh_text.strip()) > 0:
                    chinese = zhconv.convert(zh_text, 'zh-cn')
                    tokens = self.zh_tokenizer(chinese)
                    if tokens:
                        yield tokens

        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh_tmx.pt")
        if self.use_cache and os.path.exists(zh_vocab_file):
            zh_vocab = torch.load(zh_vocab_file, map_location="cpu")
        else:
            zh_vocab = build_vocab_from_iterator(
                yield_zh_tokens(),
                min_freq=1,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            zh_vocab.set_default_index(zh_vocab["<unk>"])
            if self.use_cache:
                torch.save(zh_vocab, zh_vocab_file)
        return zh_vocab

if __name__ == '__main__':
    dataset = RuZhTMXTranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\data\ru-zh.tmx")
    print("句子数量为:", dataset.row_count)
    print(dataset.ru_tokenizer("Это русский токенизатор."))  # 俄语分词示例
    print("俄文词典大小:", len(dataset.ru_vocab))
    # 输出俄文词典前10个索引
    print(dict((i, dataset.ru_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(min(10, len(dataset.zh_vocab)))))
    # 输出俄文前5个句子对应的字典索引编号
    print(dict((i, dataset.ru_tokens[i]) for i in range(min(5, len(dataset.ru_tokens)))))
    # 输出中文前5个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(min(5, len(dataset.zh_tokens)))))
    
    # 显示一些原始句子对
    print("\n原始句子对示例:")
    for i in range(min(3, len(dataset.sentence_pairs))):
        ru_text, zh_text = dataset.sentence_pairs[i]
        print(f"{i+1}. 俄语: {ru_text}")
        print(f"   中文: {zh_text}")
        print("---")
