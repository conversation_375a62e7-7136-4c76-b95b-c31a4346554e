from data.ru_zh import RuZhTranslationDataset

dataset = RuZhTranslationDataset('data/ru-zh.txt')

print('常见俄语词汇的索引:')
common_words = ['в', 'на', 'с', 'по', 'как', 'да', 'не', 'и', 'что', 'это']
for word in common_words:
    idx = dataset.ru_vocab([word])[0]
    print(f'{word}: {idx} ({dataset.ru_vocab.lookup_token(idx)})')

print('\n测试一些训练数据中的词汇:')
test_words = ['америка', 'россия', 'китай', 'европа', 'мир', 'страна']
for word in test_words:
    idx = dataset.ru_vocab([word])[0]
    print(f'{word}: {idx} ({dataset.ru_vocab.lookup_token(idx)})')
