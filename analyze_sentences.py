from data.ru_zh import RuZhTranslationDataset

dataset = RuZhTranslationDataset('data/ru-zh.txt')

test_sentences = [
    "Поезд, вероятно, прибудет на станцию до полудня.",
    "Я люблю яблоки",
    "Привет, мир!",
    "Как дела?",
    "Спасибо за помощь"
]

print("=== 词汇覆盖分析 ===")
for sentence in test_sentences:
    print(f"\n句子: {sentence}")
    tokens = dataset.ru_tokenizer(sentence)
    print(f"分词结果: {tokens}")
    
    indices = dataset.ru_vocab(tokens)
    print(f"词汇索引: {indices}")
    
    vocab_tokens = [dataset.ru_vocab.lookup_token(idx) for idx in indices]
    print(f"词汇表中的词: {vocab_tokens}")
    
    # 统计未知词汇
    unk_count = indices.count(3)  # 3 是 <unk> 的索引
    coverage = (len(indices) - unk_count) / len(indices) * 100
    print(f"词汇覆盖率: {coverage:.1f}% ({len(indices) - unk_count}/{len(indices)})")

print("\n=== 词汇表统计 ===")
print(f"俄语词汇表大小: {len(dataset.ru_vocab)}")
print(f"中文词汇表大小: {len(dataset.zh_vocab)}")

# 显示一些高频俄语词汇
print("\n=== 高频俄语词汇 (前20个) ===")
for i in range(4, min(24, len(dataset.ru_vocab))):  # 跳过特殊token
    print(f"{i}: {dataset.ru_vocab.lookup_token(i)}")
