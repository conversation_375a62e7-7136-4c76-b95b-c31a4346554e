import torch
from pathlib import Path
from data.ru_zh import RuZhTranslationDataset

# 工作目录，缓存文件和模型会放在该目录下
base_dir = r"D:\2025-up\MT\TransformerTurnDistillation\train_process\transformer-ru-zh"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/transformer_checkpoints")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/ru-zh.txt"

dataset = RuZhTranslationDataset(data_dir)
max_seq_length = 42

def translate(src: str, debug=False):
    model = torch.load(model_dir / 'best.pt', map_location=device)
    model.to(device)
    model.eval()

    # 分词并转换为索引
    ru_tokens = dataset.ru_tokenizer(src)
    if debug:
        print(f"俄语分词结果: {ru_tokens}")
    
    ru_indices = dataset.ru_vocab(ru_tokens)
    if debug:
        print(f"俄语索引: {ru_indices}")
    
    src_tensor = torch.tensor([0] + ru_indices + [1], dtype=torch.long).unsqueeze(0).to(device)
    if debug:
        print(f"源语言张量: {src_tensor}")

    # 从 <bos> 开始
    tgt = torch.tensor([[0]], dtype=torch.long, device=device)

    with torch.no_grad():
        for i in range(max_seq_length):
            out = model(src_tensor, tgt)
            predict = model.predictor(out[:, -1])
            y = torch.argmax(predict, dim=1)
            tgt = torch.cat([tgt, y.unsqueeze(0)], dim=1)
            
            if debug:
                print(f"步骤 {i}: 预测token = {y.item()}, 对应词汇 = {dataset.zh_vocab.lookup_token(y.item())}")
            
            if y.item() == 1:  # <eos>
                break

    tgt_tokens = tgt.squeeze().tolist()
    if debug:
        print(f"目标语言tokens: {tgt_tokens}")
    
    tgt_sentence = " ".join(dataset.zh_vocab.lookup_tokens(tgt_tokens))
    tgt_sentence = tgt_sentence.replace("<s>", "").replace("</s>", "").strip()
    return tgt_sentence


if __name__ == "__main__":
    print("=== 俄中翻译测试 ===")
    
    # 先用调试模式测试一个简单的句子
    print("\n--- 调试模式测试 ---")
    result = translate("в", debug=True)
    print(f"翻译结果: {result}")
    
    print("\n--- 正常翻译测试 ---")
    test_sentences = [
        "в",
        "на", 
        "как",
        "что",
        "это",
        "последние"
    ]
    
    for sentence in test_sentences:
        result = translate(sentence)
        print(f"俄语: {sentence} -> 中文: {result}")
    
    print("\n--- 复杂句子测试 ---")
    complex_sentences = [
        "в сша",
        "как это",
        "что в",
        "на европе",
        "последние годы"
    ]
    
    for sentence in complex_sentences:
        result = translate(sentence)
        print(f"俄语: {sentence}")
        print(f"中文: {result}")
        print("---")
    
    # 显示一些训练数据中的原始句子对
    print("\n--- 训练数据示例 ---")
    print("训练数据中的句子对示例:")
    with open('data/ru-zh.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for i, line in enumerate(lines[100:105]):  # 显示第100-105行
        parts = line.strip().split('\t')
        if len(parts) >= 4:
            ru_text = parts[2].strip()
            zh_text = parts[3].strip()
            if len(ru_text) > 2 and len(zh_text) > 1:
                print(f"{i+100}: {ru_text} -> {zh_text}")
