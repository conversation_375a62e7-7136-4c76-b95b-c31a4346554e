import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
from torchtext.data import get_tokenizer
from torchtext.vocab import build_vocab_from_iterator
import re


class EngItaTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.tokenizer = get_tokenizer('basic_english')
        self.use_cache = use_cache

        # 加载词典和token
        self.en_vocab = self.get_en_vocab(filepath)
        self.ita_vocab = self.get_ita_vocab(filepath)
        self.en_tokens = self.load_tokens(filepath, self.en_tokenizer, self.en_vocab, "构建英文tokens", 'en')
        self.ita_tokens = self.load_tokens(filepath, self.ita_tokenizer, self.ita_vocab, "构建意大利文tokens", 'ita')

    def __getitem__(self, index):
        return self.en_tokens[index], self.ita_tokens[index]

    def __len__(self):
        return self.row_count

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu")

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                # 英意数据格式：英语\t意大利语\t其他信息
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    if lang == 'en':
                        text = parts[0].strip()  # 英语文本
                    else:  # ita
                        text = parts[1].strip()  # 意大利语文本
                    
                    # 确保文本不为空
                    if text and len(text.strip()) > 0:
                        tokens = tokenizer(text)
                        if tokens:  # 确保分词结果不为空
                            token_indices = [vocab[token] for token in tokens]
                            token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                            tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list

    def get_row_count(self, filepath):
        count = 0
        for _ in open(filepath, encoding='utf-8'):
            count += 1
        return count

    def en_tokenizer(self, line):
        return self.tokenizer(line.lower())

    def ita_tokenizer(self, line):
        # 意大利语分词：使用简单的空格和标点符号分割
        # 可以根据需要使用更复杂的意大利语分词器
        tokens = re.findall(r'\b\w+\b', line.lower())
        return tokens

    def get_en_vocab(self, filepath):
        def yield_en_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建英文词典---")
                for line in tqdm(f, desc="构建英文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 1:
                        english = parts[0].strip()
                        # 确保文本不为空
                        if english and len(english.strip()) > 0:
                            tokens = self.en_tokenizer(english)
                            if tokens:  # 确保分词结果不为空
                                yield tokens

        dir_path = os.path.dirname(filepath)
        en_vocab_file = os.path.join(dir_path, "vocab_en.pt")
        if self.use_cache and os.path.exists(en_vocab_file):
            en_vocab = torch.load(en_vocab_file, map_location="cpu")
        else:
            en_vocab = build_vocab_from_iterator(
                yield_en_tokens(),
                min_freq=2,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            en_vocab.set_default_index(en_vocab["<unk>"])
            if self.use_cache:
                torch.save(en_vocab, en_vocab_file)
        return en_vocab

    def get_ita_vocab(self, filepath):
        def yield_ita_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建意大利文词典---")
                for line in tqdm(f, desc="构建意大利文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        italian = parts[1].strip()
                        # 确保文本不为空
                        if italian and len(italian.strip()) > 0:
                            tokens = self.ita_tokenizer(italian)
                            if tokens:  # 确保分词结果不为空
                                yield tokens

        dir_path = os.path.dirname(filepath)
        ita_vocab_file = os.path.join(dir_path, "vocab_ita.pt")
        if self.use_cache and os.path.exists(ita_vocab_file):
            ita_vocab = torch.load(ita_vocab_file, map_location="cpu")
        else:
            ita_vocab = build_vocab_from_iterator(
                yield_ita_tokens(),
                min_freq=2,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            ita_vocab.set_default_index(ita_vocab["<unk>"])
            if self.use_cache:
                torch.save(ita_vocab, ita_vocab_file)
        return ita_vocab

if __name__ == '__main__':
    dataset = EngItaTranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\data\ita-eng\ita.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.en_tokenizer("I'm an English tokenizer."))  # 英语分词示例
    print(dataset.ita_tokenizer("Sono un tokenizzatore italiano."))  # 意大利语分词示例
    print("英文词典大小:", len(dataset.en_vocab))
    # 输出英文词典前10个索引
    print(dict((i, dataset.en_vocab.lookup_token(i)) for i in range(10)))
    print("意大利文词典大小:", len(dataset.ita_vocab))
    # 输出意大利文词典前10个索引
    print(dict((i, dataset.ita_vocab.lookup_token(i)) for i in range(min(10, len(dataset.ita_vocab)))))
    # 输出英文前10个句子对应的字典索引编号
    print(dict((i, dataset.en_tokens[i]) for i in range(min(10, len(dataset.en_tokens)))))
    # 输出意大利文前10个句子对应的字典索引编号
    print(dict((i, dataset.ita_tokens[i]) for i in range(min(10, len(dataset.ita_tokens)))))
    print(dataset.en_vocab(['hello', 'world']))  # 该词在词典中的索引
    print(dataset.ita_vocab(['ciao', 'mondo']))  # 该词在词典中的索引
