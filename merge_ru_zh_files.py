#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并俄语和中文文件为平行语料
"""

def merge_ru_zh_files():
    """合并俄语和中文文件"""
    
    # 读取俄语文件
    print("正在读取俄语文件...")
    with open('data/ru.txt', 'r', encoding='utf-8') as f:
        ru_lines = f.readlines()
    
    # 读取中文文件
    print("正在读取中文文件...")
    with open('data/zh.txt', 'r', encoding='utf-8') as f:
        zh_lines = f.readlines()
    
    print(f"俄语文件行数: {len(ru_lines)}")
    print(f"中文文件行数: {len(zh_lines)}")
    
    # 确保两个文件行数相同
    min_lines = min(len(ru_lines), len(zh_lines))
    if len(ru_lines) != len(zh_lines):
        print(f"警告：文件行数不匹配，将使用较短的文件长度: {min_lines}")
    
    # 合并文件
    print("正在合并文件...")
    merged_lines = []
    valid_pairs = 0
    
    for i in range(min_lines):
        ru_text = ru_lines[i].strip()
        zh_text = zh_lines[i].strip()
        
        # 过滤掉空行
        if ru_text and zh_text and len(ru_text) > 5 and len(zh_text) > 2:
            # 格式：频率\t概率\t俄语\t中文\t其他数字\t其他数字
            merged_line = f"1\t1.0\t{ru_text}\t{zh_text}\t1\t1\n"
            merged_lines.append(merged_line)
            valid_pairs += 1
    
    print(f"有效句子对数量: {valid_pairs}")
    
    # 写入合并后的文件
    output_file = 'data/ru-zh.txt'
    print(f"正在写入合并文件: {output_file}")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(merged_lines)
    
    print(f"合并完成！生成了 {valid_pairs} 个俄中句子对")
    
    # 显示前几行作为示例
    print("\n前5行示例:")
    for i, line in enumerate(merged_lines[:5]):
        parts = line.strip().split('\t')
        if len(parts) >= 4:
            print(f"{i+1}. 俄语: {parts[2]}")
            print(f"   中文: {parts[3]}")
            print("---")

if __name__ == "__main__":
    merge_ru_zh_files()
