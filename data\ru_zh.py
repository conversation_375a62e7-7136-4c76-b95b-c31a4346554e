import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
import jieba
from torchtext.vocab import build_vocab_from_iterator
import zhconv
import re


class RuZhTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.use_cache = use_cache

        # 加载词典和token
        self.ru_vocab = self.get_ru_vocab(filepath)
        self.zh_vocab = self.get_zh_vocab(filepath)
        self.ru_tokens = self.load_tokens(filepath, self.ru_tokenizer, self.ru_vocab, "构建俄文tokens", 'ru')
        self.zh_tokens = self.load_tokens(filepath, self.zh_tokenizer, self.zh_vocab, "构建中文tokens", 'zh')

    def __getitem__(self, index):
        return self.ru_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu")

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                # 俄中数据格式：俄语在第3列（索引2），中文在第4列（索引3）
                parts = line.strip().split('\t')
                if len(parts) >= 4:
                    if lang == 'ru':
                        text = parts[2].strip()  # 俄语文本
                    else:  # zh
                        text = zhconv.convert(parts[3].strip(), 'zh-cn')  # 中文文本
                    
                    # 确保文本不为空
                    if text and len(text.strip()) > 0:
                        tokens = tokenizer(text)
                        if tokens:  # 确保分词结果不为空
                            token_indices = [vocab[token] for token in tokens]
                            token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                            tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list

    def get_row_count(self, filepath):
        count = 0
        for _ in open(filepath, encoding='utf-8'):
            count += 1
        return count

    def ru_tokenizer(self, line):
        # 俄语分词：使用简单的空格和标点符号分割
        tokens = re.findall(r'\b\w+\b', line.lower())
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_ru_vocab(self, filepath):
        def yield_ru_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建俄文词典---")
                for line in tqdm(f, desc="构建俄文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        russian = parts[2].strip()
                        # 确保文本不为空
                        if russian and len(russian.strip()) > 0:
                            tokens = self.ru_tokenizer(russian)
                            if tokens:  # 确保分词结果不为空
                                yield tokens

        dir_path = os.path.dirname(filepath)
        ru_vocab_file = os.path.join(dir_path, "vocab_ru.pt")
        if self.use_cache and os.path.exists(ru_vocab_file):
            ru_vocab = torch.load(ru_vocab_file, map_location="cpu")
        else:
            ru_vocab = build_vocab_from_iterator(
                yield_ru_tokens(),
                min_freq=2,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            ru_vocab.set_default_index(ru_vocab["<unk>"])
            if self.use_cache:
                torch.save(ru_vocab, ru_vocab_file)
        return ru_vocab

    def get_zh_vocab(self, filepath):
        def yield_zh_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建中文词典---")
                for line in tqdm(f, desc="构建中文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        chinese = zhconv.convert(parts[3].strip(), 'zh-cn')
                        # 确保文本不为空
                        if chinese and len(chinese.strip()) > 0:
                            tokens = self.zh_tokenizer(chinese)
                            if tokens:  # 确保分词结果不为空
                                yield tokens

        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh.pt")
        if self.use_cache and os.path.exists(zh_vocab_file):
            zh_vocab = torch.load(zh_vocab_file, map_location="cpu")
        else:
            zh_vocab = build_vocab_from_iterator(
                yield_zh_tokens(),
                min_freq=1,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            zh_vocab.set_default_index(zh_vocab["<unk>"])
            if self.use_cache:
                torch.save(zh_vocab, zh_vocab_file)
        return zh_vocab

if __name__ == '__main__':
    dataset = RuZhTranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\data\ru-zh.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.ru_tokenizer("Это русский токенизатор."))  # 俄语分词示例
    print("俄文词典大小:", len(dataset.ru_vocab))
    # 输出俄文词典前10个索引
    print(dict((i, dataset.ru_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(min(10, len(dataset.zh_vocab)))))
    # 输出俄文前5个句子对应的字典索引编号
    print(dict((i, dataset.ru_tokens[i]) for i in range(min(5, len(dataset.ru_tokens)))))
    # 输出中文前5个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(min(5, len(dataset.zh_tokens)))))
    print(dataset.ru_vocab(['привет', 'мир']))  # 该词在词典中的索引
    print(dataset.zh_vocab(['你', '好', '世', '界']))  # 该词在词典中的索引
