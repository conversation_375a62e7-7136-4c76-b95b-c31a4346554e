import os
import torch
from torch.utils.data import Dataset
from tqdm import tqdm
from torchtext.data import get_tokenizer
from torchtext.vocab import build_vocab_from_iterator
import re


class EngItaTranslationDataset(Dataset):
    def __init__(self, filepath, use_cache=True):
        self.row_count = self.get_row_count(filepath)
        self.tokenizer = get_tokenizer('basic_english')
        self.use_cache = use_cache

        # 加载词典和token
        self.en_vocab = self.get_en_vocab(filepath)
        self.ita_vocab = self.get_ita_vocab(filepath)
        self.en_tokens = self.load_tokens(filepath, self.en_tokenizer, self.en_vocab, "构建英文tokens", 'en')
        self.ita_tokens = self.load_tokens(filepath, self.ita_tokenizer, self.ita_vocab, "构建意大利文tokens", 'ita')

    def __getitem__(self, index):
        return self.ru_tokens[index], self.zh_tokens[index]

    def __len__(self):
        return self.row_count

    def load_tokens(self, filepath, tokenizer, vocab, desc, lang):
        dir_path = os.path.dirname(filepath)
        cache_file = os.path.join(dir_path, f"tokens_list_{lang}.pt")
        if self.use_cache and os.path.exists(cache_file):
            print(f"正在加载缓存文件[{cache_file}]，请稍候...")
            return torch.load(cache_file, map_location="cpu")

        tokens_list = []
        with open(filepath, encoding='utf-8') as f:
            for line in tqdm(f, desc=desc, total=self.row_count):
                # 俄中数据格式：每行包含多个字段，俄语在第4列，中文在第5列
                parts = line.strip().split('\t')
                if len(parts) >= 5:
                    if lang == 'ru':
                        text = parts[3].strip()  # 俄语文本
                    else:  # zh
                        text = zhconv.convert(parts[4].strip(), 'zh-cn')  # 中文文本

                    if text:  # 确保文本不为空
                        tokens = tokenizer(text)
                        token_indices = [vocab[token] for token in tokens]
                        token_tensor = torch.tensor([vocab["<s>"]] + token_indices + [vocab["</s>"]])
                        tokens_list.append(token_tensor)

        if self.use_cache:
            torch.save(tokens_list, cache_file)
        return tokens_list



    def get_row_count(self, filepath):
        count = 0
        for _ in open(filepath, encoding='utf-8'):
            count += 1
        return count

    def ru_tokenizer(self, line):
        # 俄语分词：使用简单的空格和标点符号分割
        # 可以根据需要使用更复杂的俄语分词器
        tokens = re.findall(r'\b\w+\b', line.lower())
        return tokens

    def zh_tokenizer(self, line):
        return list(jieba.cut(line))

    def get_ru_vocab(self, filepath):
        def yield_ru_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建俄文词典---")
                for line in tqdm(f, desc="构建俄文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        russian = parts[3].strip()
                        if russian:
                            yield self.ru_tokenizer(russian)

        dir_path = os.path.dirname(filepath)
        ru_vocab_file = os.path.join(dir_path, "vocab_ru.pt")
        if self.use_cache and os.path.exists(ru_vocab_file):
            ru_vocab = torch.load(ru_vocab_file, map_location="cpu")
        else:
            ru_vocab = build_vocab_from_iterator(
                yield_ru_tokens(),
                min_freq=2,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            ru_vocab.set_default_index(ru_vocab["<unk>"])
            if self.use_cache:
                torch.save(ru_vocab, ru_vocab_file)
        return ru_vocab

    def get_zh_vocab(self, filepath):
        def yield_zh_tokens():
            with open(filepath, encoding='utf-8') as f:
                print("---开始构建中文词典---")
                for line in tqdm(f, desc="构建中文词典", total=self.row_count):
                    parts = line.split('\t')
                    if len(parts) >= 5:
                        chinese = zhconv.convert(parts[4].strip(), 'zh-cn')
                        if chinese:
                            yield self.zh_tokenizer(chinese)

        dir_path = os.path.dirname(filepath)
        zh_vocab_file = os.path.join(dir_path, "vocab_zh.pt")
        if self.use_cache and os.path.exists(zh_vocab_file):
            zh_vocab = torch.load(zh_vocab_file, map_location="cpu")
        else:
            zh_vocab = build_vocab_from_iterator(
                yield_zh_tokens(),
                min_freq=1,
                specials=["<s>", "</s>", "<pad>", "<unk>"]
            )
            zh_vocab.set_default_index(zh_vocab["<unk>"])
            if self.use_cache:
                torch.save(zh_vocab, zh_vocab_file)
        return zh_vocab

if __name__ == '__main__':
    dataset = RuZhTranslationDataset(r"D:\2025-up\MT\TransformerTurnDistillation\data\ru-zh.txt")
    print("句子数量为:", dataset.row_count)
    print(dataset.ru_tokenizer("Это русский токенизатор."))  # 俄语分词示例
    print("俄文词典大小:", len(dataset.ru_vocab))
    # 输出俄文词典前10个索引
    print(dict((i, dataset.ru_vocab.lookup_token(i)) for i in range(10)))
    print("中文词典大小:", len(dataset.zh_vocab))
    # 输出中文词典前10个索引
    print(dict((i, dataset.zh_vocab.lookup_token(i)) for i in range(10)))
    # 输出俄文前10个句子对应的字典索引编号
    print(dict((i, dataset.ru_tokens[i]) for i in range(min(10, len(dataset.ru_tokens)))))
    # 输出中文前10个句子对应的字典索引编号
    print(dict((i, dataset.zh_tokens[i]) for i in range(min(10, len(dataset.zh_tokens)))))
    print(dataset.ru_vocab(['привет', 'мир']))  # 该词在词典中的索引
    print(dataset.zh_vocab(['你', '好', '世', '界']))  # 该词在词典中的索引